By deeply analyzing the `HN-sources`, identify every tiny elements of the HTML, CSS, and js to pin point perfectly their roles, purpose, and intention.
Next plan the implementation (CSS and js - vanilla) of everything needed to do a plugin that overhaul the visual of the Hacker news platform.
The plugin inject js and CSS that redo the whole UI using flexbox. The must be the last thing to be injected to the page. The page retains ALL it colors palette and usage + fonts.

The navigation bar retains its element but with modern styling, spacing and grouping of the elements. The `	Hacker News` at the right, the `new | past | comments | ask | show | ` at the center, and the `jobs | submit` at the right end, styled with button.

The page displays the topics like Forum topic card with proper styling.
We need to have a clear distinction to links, inputs, forms, buttons, action, states (hover, active, selected, active, visited - not applicable to buttons)

The project will heavily and deeply manage the selections so that what lead to outside , what is a comment, comment of comment, comment of comment of comment ..., what is indented.

From the topics list, pagination, footer links, to comment threads and actions everything must be clearly identified and styled.

Do a ground analysis and choices.
All the plugin codes must be inside `hacker-news-css` folder.
Inside `HN-sources` folder, real source code copied from the major pages.
Somme elements must be directly targeted by tag + id + class + position + order + etc. to avoid conflicts

Strictly adhere to Google Material design (latest 2025) standards