# Design Plan: Hacker News Visual Overhaul Plugin

## 1. Executive Summary & Goals
This document outlines the plan to develop a browser plugin that completely overhauls the user interface of Hacker News. The plugin will inject custom CSS and vanilla JavaScript to refactor the site's legacy, table-based layout into a modern, responsive design using Flexbox, while strictly adhering to Google's Material Design 3 standards.

-   **Primary Objective:** To modernize the Hacker News user experience through a complete visual redesign without altering the site's core HTML structure or functionality.
-   **Key Goals:**
    1.  Implement a clean, card-based, responsive layout for stories and comments using Flexbox.
    2.  Improve readability and usability by applying consistent Material Design principles for typography, spacing, components (buttons, forms), and interactive states.
    3.  Preserve the original Hacker News brand identity by retaining the exact color palette and font families.

## 2. Current Situation Analysis
The existing Hacker News frontend is built on a legacy HTML structure that relies heavily on `<table>` elements for layout, with `<center>` tags for centering. This architecture is not responsive and uses outdated techniques, such as spacer GIFs (`s.gif`) with varying widths, to create indentation in comment threads. The provided `default.css` file contains a minimal set of styles, resulting in a user interface that lacks modern affordances for interaction and readability. The site's functionality is driven by a small, efficient vanilla JavaScript file (`script_footer.js`) that directly manipulates the DOM based on specific element IDs and classes.

-   **Key Pain Points:**
    -   **Non-Responsive Layout:** The table-based design does not adapt well to different screen sizes.
    -   **Outdated UI Patterns:** Comment indentation and element spacing are handled by inflexible and inaccessible methods (spacer images).
    -   **Lack of Visual Hierarchy:** There is a poor distinction between interactive elements like links, actions (upvote, hide), and static text.
    -   **Inconsistent Component Styling:** Forms, buttons, and other interactive elements lack modern styling and clear visual states (hover, active).

## 3. Proposed Solution / Refactoring Strategy
### 3.1. High-Level Design / Architectural Overview
The solution will be a browser extension that injects a single CSS file and a single JavaScript file into every page on `news.ycombinator.com`. The injection will occur at `document_end` to ensure our styles and scripts are the last to be applied, granting them override priority.

1.  **CSS-First Approach:** The vast majority of the visual changes will be accomplished with CSS. We will override the default styles and use Flexbox to redefine the page structure. Specificity will be managed carefully by using IDs, chained classes, and attribute selectors to target elements precisely.
2.  **Minimalist JavaScript for DOM Preparation:** Vanilla JavaScript will be used for one-time DOM manipulations on page load. Its sole purpose is to restructure the existing HTML where CSS alone is insufficient. For example, it will group the multiple `<tr>` elements that constitute a single story into a single parent `<div>` to create a "card" component. This script will be carefully written to preserve all original element IDs and classes required by Hacker News's native `script_footer.js`.
3.  **Component-Based Styling:** We will treat distinct parts of the UI (Header, Story Card, Comment) as components and define their styles in a modular fashion within the CSS file.

### 3.2. Key Components / Modules
-   **Global Styles & Layout:** The main container that replaces the `<center>` and `#hnmain` table.
-   **Header Navigation:** The top bar containing the logo, navigation links, and user actions.
-   **Story Card:** A self-contained visual block for each story on the homepage and other list pages.
-   **Comment Thread:** A component for displaying a single comment, including its metadata, content, and actions. It will handle visual indentation.
-   **Forms & Interactive Elements:** A consistent set of styles for all buttons, text inputs, text areas, and links, complete with Material Design states.
-   **Footer:** The bottom section of the page with links and the search form.

### 3.3. Detailed Action Plan / Phases
---
#### **Phase 1: Analysis and Foundation**
-   **Objective(s):** Establish the project structure and perform a deep analysis of the existing assets to extract design tokens and identify all necessary DOM selectors.
-   **Priority:** High

-   **Task 1.1: Project Setup**
    -   **Rationale/Goal:** Create the necessary files and update the manifest to include the JavaScript injector.
    -   **Estimated Effort:** S
    -   **Deliverable/Criteria for Completion:** The `hacker-news-css` folder contains a `js/main.js` file, and the `manifest.json` is updated to include it in the `content_scripts` section.

-   **Task 1.2: Extract Design Tokens**
    -   **Rationale/Goal:** To strictly adhere to the "retain all colors and fonts" rule by extracting them from `default.css` and defining them as CSS Custom Properties for reuse.
    -   **Estimated Effort:** S
    -   **Deliverable/Criteria for Completion:** A `:root` block in `style.css` containing all official HN colors (e.g., `--hn-orange: #ff6600;`, `--hn-background: #f6f6ef;`) and font families (e.g., `--font-primary: Verdana, Geneva, sans-serif;`).

-   **Task 1.3: Create Selector Map**
    -   **Rationale/Goal:** Analyze all provided `HN-sources/*.html` files to document the specific, stable selectors needed to target every UI element (e.g., story title, upvote arrow, comment text, indentation element).
    -   **Estimated Effort:** M
    -   **Deliverable/Criteria for Completion:** An internal document or a commented section in the CSS/JS files listing the selectors for all key elements to be styled.

---
#### **Phase 2: Global Layout and Header Redesign**
-   **Objective(s):** Implement the main page structure and the modernized navigation bar.
-   **Priority:** High

-   **Task 2.1: Implement Flexbox Main Container**
    -   **Rationale/Goal:** Replace the core `<table>`-based layout with a modern Flexbox container that is centered and responsive.
    -   **Estimated Effort:** M
    -   **Deliverable/Criteria for Completion:** The main content area of the site is centered on the page within a flexible container, and the original `<table>` layout is successfully overridden.

-   **Task 2.2: Re-style Header Navigation**
    -   **Rationale/Goal:** Apply Flexbox to the header to achieve the specified left-center-right grouping of navigation elements.
    -   **Estimated Effort:** M
    -   **Deliverable/Criteria for Completion:** The header displays the "Hacker News" logo on the left, main navigation links in the center, and "jobs" and "submit" links styled as Material Design buttons on the right.

---
#### **Phase 3: Story List and Card Implementation**
-   **Objective(s):** Transform the story list into a modern card-based interface.
-   **Priority:** High

-   **Task 3.1: Develop Story Wrapper Script**
    -   **Rationale/Goal:** The HTML for each story is split across two `<tr>` elements. A script is needed to wrap them in a single parent `<div class="story-card">` to allow for card-based styling.
    -   **Estimated Effort:** M
    -   **Deliverable/Criteria for Completion:** On the homepage, the `main.js` script correctly identifies each story and wraps its corresponding `<tr>` elements in a single `<div>` without breaking any existing functionality.

-   **Task 3.2: Style the Story Card Component**
    -   **Rationale/Goal:** Apply Material Design principles (padding, margins, subtle shadows for elevation) to the new `.story-card` element.
    -   **Estimated Effort:** M
    -   **Deliverable/Criteria for Completion:** Stories on the homepage appear as distinct, well-formatted cards. All internal elements (rank, title, votelinks, subtext) are properly aligned and spaced within the card.

---
#### **Phase 4: Comment Page Refactoring**
-   **Objective(s):** Modernize the display of comment threads, focusing on readability and clear hierarchy.
-   **Priority:** Medium

-   **Task 4.1: Style Comment Indentation via CSS**
    -   **Rationale/Goal:** Replace the outdated spacer GIF method for indentation with a modern, maintainable CSS approach.
    -   **Estimated Effort:** M
    -   **Deliverable/Criteria for Completion:** Use CSS attribute selectors (e.g., `.ind[indent="1"]`) to apply `padding-left` to comment containers. The visual hierarchy of nested comments is clear and proportional. The spacer GIFs are hidden via `display: none;`.

-   **Task 4.2: Style Comment Component**
    -   **Rationale/Goal:** Style the comment header (`.comhead`), body (`.comment`), and reply link to improve readability and create a consistent look with the story cards.
    -   **Estimated Effort:** L
    -   **Deliverable/Criteria for Completion:** Each comment is visually distinct. Add subtle visual cues like left-border lines whose color can vary slightly with depth to enhance the sense of nesting.

-   **Task 4.3: Ensure Comment Interactivity**
    -   **Rationale/Goal:** Verify that the new styling does not interfere with the native JavaScript for voting and collapsing/expanding comments.
    -   **Estimated Effort:** M
    -   **Deliverable/Criteria for Completion:** Upvoting comments and using the `[–]` toggle works flawlessly. The toggle element itself is styled to match the new design.

---
#### **Phase 5: Forms, Static Pages, and Final Polish**
-   **Objective(s):** Ensure a consistent user experience across all pages and interactive states.
-   **Priority:** Medium

-   **Task 5.1: Style Forms and Inputs**
    -   **Rationale/Goal:** Apply Material Design text field and button styles to the login page, comment forms, and the footer search bar.
    -   **Estimated Effort:** M
    -   **Deliverable/Criteria for Completion:** All `input`, `textarea`, and `submit` elements have a consistent, modern look that aligns with Material Design guidelines.

-   **Task 5.2: Style Static Pages**
    -   **Rationale/Goal:** Ensure the styles for typography, links, and layout apply gracefully to the FAQ, Security, and other text-heavy pages.
    -   **Estimated Effort:** S
    -   **Deliverable/Criteria for Completion:** The static pages are readable and visually consistent with the rest of the redesigned site.

-   **Task 5.3: Implement All Element States**
    -   **Rationale/Goal:** Provide clear visual feedback for user interactions.
    -   **Estimated Effort:** M
    -   **Deliverable/Criteria for Completion:** All links and interactive elements have distinct `:hover`, `:active`, and `:visited` styles that follow Material Design principles (e.g., subtle background color changes, underline transitions).

## 4. Key Considerations & Risk Mitigation
### 4.1. Technical Risks & Challenges
-   **Risk:** Future changes to the Hacker News HTML structure or CSS class names could break the plugin.
    -   **Mitigation:** This is an inherent risk. We will use the most stable selectors available (e.g., IDs, `.athing` class) and document them clearly. The plugin will require maintenance if the source site is updated.
-   **Risk:** The DOM manipulation script could conflict with or break the site's native `script_footer.js`.
    -   **Mitigation:** The script will run once on load and will be designed defensively. It will not remove or alter IDs, and it will preserve the original element hierarchy as much as possible, only adding wrapper elements. Thorough testing of voting, collapsing, and hiding stories is required.
-   **Risk:** Overly complex CSS selectors could lead to performance degradation.
    -   **Mitigation:** We will avoid deep nesting and overly broad selectors where possible. The overall size of the injected assets will be kept minimal.

### 4.2. Dependencies
-   **Internal:** The phases are sequential. The analysis in Phase 1 is critical for all subsequent phases. The story card script from Phase 3 must be complete before its CSS can be finalized.
-   **External:** The plan is entirely dependent on the current structure of `news.ycombinator.com` as represented in the `HN-sources` files.