*, *::after, *::before {
  box-sizing: border-box;
}


html, body {
  width: 100%;
  padding: 0%;
}

/**
* Turning table into a flexbox
* Turning table rows into flex items
* Turning table cells into flex items
*/
table {
  display: flex;
  flex-direction: column;
}

tr {
  display: flex;
}

td {
  flex: 0 0 auto;
}

/**
* The <center> used as main turn it to a container
*/
center {
  display: flex;
  flex-direction: column;
}
